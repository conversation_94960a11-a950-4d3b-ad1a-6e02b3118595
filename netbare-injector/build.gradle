apply plugin: 'com.android.library'

android {
    compileSdkVersion 28
    namespace 'com.github.netbare.injector'  // 添加这一行
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation project(':netbare-core')
    implementation 'com.android.support:appcompat-v7:28.0.0'
}