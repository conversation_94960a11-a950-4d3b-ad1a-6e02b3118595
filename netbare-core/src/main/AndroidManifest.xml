<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.github.megatronking.netbare">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application>
        <activity android:name=".ssl.CertificateInstallActivity"
            android:theme="@style/NetBare"
            android:excludeFromRecents="true"/>
    </application>

</manifest>
