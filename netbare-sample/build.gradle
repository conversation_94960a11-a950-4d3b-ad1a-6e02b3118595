apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'com.github.netbare.sample'  // 添加这一行
    compileSdkVersion 28
    defaultConfig {
        applicationId "com.github.megatronking.netbare.sample"
        minSdkVersion 21
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.11'

    implementation 'com.google.code.gson:gson:2.8.2'
    implementation 'me.weishu:free_reflection:3.0.1'

    // NetBare libraries
    implementation project(':netbare-core')
    implementation project(':netbare-injector')
}
