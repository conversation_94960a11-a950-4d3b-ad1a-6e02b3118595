@echo off
echo 正在移除 netbare-sample 示例模块...

REM 检查目录是否存在
if exist "netbare-sample" (
    echo 删除 netbare-sample 目录...
    rmdir /s /q netbare-sample
    if %errorlevel% equ 0 (
        echo ✓ netbare-sample 目录已删除
    ) else (
        echo ✗ 删除 netbare-sample 目录失败
    )
) else (
    echo netbare-sample 目录不存在，跳过删除
)

echo.
echo 示例模块已移除！
echo.
echo 现在项目只包含核心模块：
echo - netbare-core: NetBare 核心功能
echo - netbare-injector: HTTP 注入器功能
echo.
echo 您可以继续使用我们创建的 https-interceptor 项目。
echo.
pause
