# NetBare 项目清理指南

## 问题说明

您遇到的问题有两个：

1. **Ko<PERSON><PERSON> Gradle 插件兼容性问题**: `Could not initialize class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt`
2. **不需要的示例模块**: `netbare-sample` 只是示例代码，可以安全移除

## 解决方案

### 步骤 1: 移除示例模块

`netbare-sample` 模块确实只是示例，移除它不会影响核心功能。

**手动删除方法**:
1. 删除 `netbare-sample` 整个目录
2. 已经从 `settings.gradle` 中移除了该模块引用

**或者运行清理脚本**:
```bash
# Windows
remove-sample.bat

# Linux/macOS
chmod +x remove-sample.sh
./remove-sample.sh
```

### 步骤 2: 使用我们创建的新项目

既然您需要 HTTPS 拦截功能，建议直接使用我们创建的 `https-interceptor` 项目，它已经：

1. ✅ 解决了所有依赖问题
2. ✅ 使用了现代的 Android 开发配置
3. ✅ 包含了完整的 HTTPS 拦截功能
4. ✅ 提供了可视化配置界面

## 推荐做法

### 方案 A: 使用新创建的项目（推荐）

直接使用 `https-interceptor` 项目：

```bash
cd https-interceptor

# 复制 NetBare 核心库
cp -r ../netbare-core ./
cp -r ../netbare-injector ./

# 运行设置脚本
./setup.sh  # Linux/macOS
# 或
setup.bat   # Windows

# 在 Android Studio 中打开项目
```

### 方案 B: 修复原项目（不推荐）

如果您坚持使用原项目，需要：

1. **更新 Kotlin 版本**:
   ```gradle
   // build.gradle (项目级)
   buildscript {
       ext.kotlin_version = '1.6.21'  // 更新版本
       dependencies {
           classpath 'com.android.tools.build:gradle:7.2.2'
           classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
       }
   }
   ```

2. **更新 Gradle Wrapper**:
   ```bash
   ./gradlew wrapper --gradle-version=7.5
   ```

3. **移除示例模块**:
   - 删除 `netbare-sample` 目录
   - 从 `settings.gradle` 移除引用（已完成）

## 项目结构对比

### 清理后的 NetBare 项目结构:
```
NetBare-Android-master/
├── netbare-core/           # 核心功能库
├── netbare-injector/       # HTTP 注入器库
├── build.gradle           # 项目构建配置
├── settings.gradle        # 项目设置
└── README.md             # 项目说明
```

### 新的 HTTPS Interceptor 项目结构:
```
https-interceptor/
├── app/                   # 主应用
├── netbare-core/         # NetBare 核心库（需复制）
├── netbare-injector/     # NetBare 注入器库（需复制）
├── build.gradle         # 项目构建配置
├── settings.gradle      # 项目设置
└── README.md           # 使用说明
```

## 功能对比

| 功能 | 原 NetBare Sample | 新 HTTPS Interceptor |
|------|------------------|---------------------|
| HTTPS 拦截 | ✅ | ✅ |
| 响应修改 | ✅ | ✅ |
| 可视化配置 | ❌ | ✅ |
| 现代 UI | ❌ | ✅ |
| 测试工具 | ❌ | ✅ |
| 详细文档 | ❌ | ✅ |
| 依赖问题 | ❌ | ✅ |

## 立即开始

### 快速启动新项目:

1. **进入新项目目录**:
   ```bash
   cd https-interceptor
   ```

2. **复制 NetBare 库**:
   ```bash
   cp -r ../netbare-core ./
   cp -r ../netbare-injector ./
   ```

3. **在 Android Studio 中打开**:
   - File → Open → 选择 `https-interceptor` 目录
   - 等待 Gradle 同步完成
   - 构建并运行项目

### 验证功能:

1. 安装应用到设备
2. 安装自签证书
3. 授予 VPN 权限
4. 配置拦截规则
5. 测试 HTTPS 请求拦截

## 常见问题

### Q: 删除示例模块会影响功能吗？
A: 不会。`netbare-sample` 只是演示如何使用 NetBare 库的示例代码，删除它不会影响核心功能。

### Q: 为什么推荐使用新项目？
A: 新项目解决了以下问题：
- 依赖版本兼容性
- 现代 Android 开发实践
- 更好的用户界面
- 完整的功能实现

### Q: 如何迁移现有配置？
A: 新项目提供了更好的配置方式，您可以：
- 使用可视化界面重新配置
- 或者参考示例代码进行迁移

## 总结

建议您：

1. ✅ **使用新的 `https-interceptor` 项目**
2. ✅ **删除不需要的 `netbare-sample` 模块**
3. ✅ **享受更好的开发体验和功能**

新项目不仅解决了您遇到的技术问题，还提供了更完整和现代的 HTTPS 拦截解决方案。
