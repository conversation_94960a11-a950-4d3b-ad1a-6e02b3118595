@echo off
echo Setting up HTTPS Interceptor project...

REM Check if NetBare libraries exist
if not exist "netbare-core" (
    echo Error: netbare-core directory not found!
    echo Please copy the netbare-core directory from NetBare project to this location.
    echo Example: copy /path/to/NetBare-Android-master/netbare-core ./
    pause
    exit /b 1
)

if not exist "netbare-injector" (
    echo Error: netbare-injector directory not found!
    echo Please copy the netbare-injector directory from NetBare project to this location.
    echo Example: copy /path/to/NetBare-Android-master/netbare-injector ./
    pause
    exit /b 1
)

echo NetBare libraries found!

REM Create symbolic links or copy NetBare libraries
echo Creating links to NetBare libraries...

REM For Windows, we'll copy the directories instead of creating symbolic links
if not exist "netbare-core\build.gradle" (
    echo Copying netbare-core from parent directory...
    xcopy "..\netbare-core" "netbare-core" /E /I /Y
)

if not exist "netbare-injector\build.gradle" (
    echo Copying netbare-injector from parent directory...
    xcopy "..\netbare-injector" "netbare-injector" /E /I /Y
)

echo Setup completed!
echo.
echo Next steps:
echo 1. Open the project in Android Studio
echo 2. Sync the project with Gradle files
echo 3. Build and run the application
echo.
echo Note: Make sure you have the NetBare libraries in the correct location.
echo.
pause
