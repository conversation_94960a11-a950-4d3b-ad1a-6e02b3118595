@echo off
echo Setting up HTTPS Interceptor project...

REM Check if we're in the correct directory
if not exist "app\build.gradle" (
    echo Error: Please run this script from the https-interceptor project root directory
    pause
    exit /b 1
)

echo 1. Copying NetBare libraries...

REM Copy NetBare libraries from parent directory
if not exist "netbare-core" (
    if exist "..\netbare-core" (
        echo Copying netbare-core...
        xcopy "..\netbare-core" "netbare-core" /E /I /Y
    ) else (
        echo Error: netbare-core not found in parent directory!
        echo Please ensure NetBare libraries are available.
        pause
        exit /b 1
    )
) else (
    echo netbare-core already exists
)

if not exist "netbare-injector" (
    if exist "..\netbare-injector" (
        echo Copying netbare-injector...
        xcopy "..\netbare-injector" "netbare-injector" /E /I /Y
    ) else (
        echo Error: netbare-injector not found in parent directory!
        echo Please ensure NetBare libraries are available.
        pause
        exit /b 1
    )
) else (
    echo netbare-injector already exists
)

echo 2. Setting up Gradle Wrapper...

REM Copy gradle wrapper from parent directory if available
if exist "..\gradle\wrapper\gradle-wrapper.jar" (
    if not exist "gradle\wrapper\gradle-wrapper.jar" (
        echo Copying Gradle Wrapper JAR...
        copy "..\gradle\wrapper\gradle-wrapper.jar" "gradle\wrapper\gradle-wrapper.jar"
    )
)

REM Make gradlew executable (not needed on Windows, but good practice)
echo Gradle wrapper setup completed

echo 3. Verifying project structure...

REM Check essential files
if exist "app\build.gradle" if exist "build.gradle" if exist "settings.gradle" (
    echo ✓ Project structure verified
) else (
    echo ✗ Project structure incomplete
    pause
    exit /b 1
)

echo.
echo ✓ Setup completed successfully!
echo.
echo Next steps:
echo 1. Open Android Studio
echo 2. Choose "Open an existing Android Studio project"
echo 3. Select this directory: %CD%
echo 4. Wait for Gradle sync to complete
echo 5. Build and run the application
echo.
echo If you encounter any issues:
echo - Make sure Android Studio is updated
echo - Check that NetBare libraries are properly copied
echo - Try "File -> Invalidate Caches and Restart" in Android Studio
echo.
pause
