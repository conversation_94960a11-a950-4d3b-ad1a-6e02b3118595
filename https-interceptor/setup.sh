#!/bin/bash

echo "Setting up HTTPS Interceptor project..."

# Check if we're in the correct directory
if [ ! -f "app/build.gradle" ]; then
    echo "Error: Please run this script from the https-interceptor project root directory"
    exit 1
fi

echo "1. Copying NetBare libraries..."

# Copy NetBare libraries from parent directory
if [ ! -d "netbare-core" ]; then
    if [ -d "../netbare-core" ]; then
        echo "Copying netbare-core..."
        cp -r ../netbare-core ./
    else
        echo "Error: netbare-core not found in parent directory!"
        echo "Please ensure NetBare libraries are available."
        exit 1
    fi
else
    echo "netbare-core already exists"
fi

if [ ! -d "netbare-injector" ]; then
    if [ -d "../netbare-injector" ]; then
        echo "Copying netbare-injector..."
        cp -r ../netbare-injector ./
    else
        echo "Error: netbare-injector not found in parent directory!"
        echo "Please ensure NetBare libraries are available."
        exit 1
    fi
else
    echo "netbare-injector already exists"
fi

echo "2. Setting up Gradle Wrapper..."

# Copy gradle wrapper from parent directory if available
if [ -f "../gradle/wrapper/gradle-wrapper.jar" ]; then
    if [ ! -f "gradle/wrapper/gradle-wrapper.jar" ]; then
        echo "Copying Gradle Wrapper JAR..."
        cp ../gradle/wrapper/gradle-wrapper.jar gradle/wrapper/
    fi
fi

# Make gradlew executable
chmod +x gradlew
echo "Gradle wrapper setup completed"

echo "3. Verifying project structure..."

# Check essential files
if [ -f "app/build.gradle" ] && [ -f "build.gradle" ] && [ -f "settings.gradle" ]; then
    echo "✓ Project structure verified"
else
    echo "✗ Project structure incomplete"
    exit 1
fi

echo ""
echo "✓ Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Open Android Studio"
echo "2. Choose 'Open an existing Android Studio project'"
echo "3. Select this directory: $(pwd)"
echo "4. Wait for Gradle sync to complete"
echo "5. Build and run the application"
echo ""
echo "If you encounter any issues:"
echo "- Make sure Android Studio is updated"
echo "- Check that NetBare libraries are properly copied"
echo "- Try 'File -> Invalidate Caches and Restart' in Android Studio"
echo ""
