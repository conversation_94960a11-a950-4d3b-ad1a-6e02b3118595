#!/bin/bash

echo "Setting up HTTPS Interceptor project..."

# Check if NetBare libraries exist
if [ ! -d "netbare-core" ]; then
    echo "Error: netbare-core directory not found!"
    echo "Please copy the netbare-core directory from NetBare project to this location."
    echo "Example: cp -r /path/to/NetBare-Android-master/netbare-core ./"
    exit 1
fi

if [ ! -d "netbare-injector" ]; then
    echo "Error: netbare-injector directory not found!"
    echo "Please copy the netbare-injector directory from NetBare project to this location."
    echo "Example: cp -r /path/to/NetBare-Android-master/netbare-injector ./"
    exit 1
fi

echo "NetBare libraries found!"

# Create symbolic links or copy NetBare libraries
echo "Creating links to NetBare libraries..."

# Try to create symbolic links first, fallback to copying
if [ ! -f "netbare-core/build.gradle" ]; then
    echo "Copying netbare-core from parent directory..."
    cp -r ../netbare-core ./
fi

if [ ! -f "netbare-injector/build.gradle" ]; then
    echo "Copying netbare-injector from parent directory..."
    cp -r ../netbare-injector ./
fi

# Make gradlew executable
chmod +x gradlew

echo "Setup completed!"
echo ""
echo "Next steps:"
echo "1. Open the project in Android Studio"
echo "2. Sync the project with Gradle files"
echo "3. Build and run the application"
echo ""
echo "Note: Make sure you have the NetBare libraries in the correct location."
echo ""
