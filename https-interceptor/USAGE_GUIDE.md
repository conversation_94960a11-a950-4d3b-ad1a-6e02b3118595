# HTTPS Interceptor 使用指南

## 快速开始

### 1. 项目设置

首先，您需要将 NetBare 库复制到项目中：

```bash
# 从 NetBare 项目复制核心库
cp -r /path/to/NetBare-Android-master/netbare-core ./https-interceptor/
cp -r /path/to/NetBare-Android-master/netbare-injector ./https-interceptor/

# 或者在 Windows 上
copy /path/to/NetBare-Android-master/netbare-core ./https-interceptor/
copy /path/to/NetBare-Android-master/netbare-injector ./https-interceptor/
```

### 2. 构建项目

```bash
cd https-interceptor
./gradlew build
```

### 3. 安装应用

将应用安装到 Android 设备上（需要 Android 5.0+ 和开发者模式）。

## 详细使用步骤

### 步骤 1: 证书安装

1. 首次启动应用时，点击"开始拦截"
2. 系统会提示安装自签证书
3. 进入 **设置 > 安全 > 加密与凭据 > 从存储设备安装**
4. 选择证书文件并安装（证书名称：HttpsInterceptor）

### 步骤 2: VPN 权限

1. 安装证书后，再次点击"开始拦截"
2. 系统会请求 VPN 权限
3. 点击"确定"授予权限

### 步骤 3: 配置拦截规则

1. 点击"拦截配置"按钮
2. 添加目标 URL：
   - 点击"+ 添加目标URL"
   - 输入要拦截的域名或URL部分（如：`api.example.com`）
3. 添加响应规则：
   - 点击"+ 添加响应规则"
   - 输入URL模式（如：`/api/user`）
   - 输入新的响应内容

### 步骤 4: 开始拦截

1. 返回主界面
2. 点击"开始拦截"
3. 状态显示为"正在拦截HTTPS请求"时表示成功启动

## 配置示例

### 示例 1: 修改 JSON API 响应

**场景**: 修改用户信息 API 的返回数据

**目标URL**: `jsonplaceholder.typicode.com`

**URL模式**: `users/1`

**新响应**:
```json
{
  "id": 1,
  "name": "Modified User",
  "username": "modified_user",
  "email": "<EMAIL>",
  "phone": "************** x56442",
  "website": "modified.org",
  "company": {
    "name": "Modified Company",
    "catchPhrase": "This response was intercepted",
    "bs": "harness real-time e-markets"
  },
  "intercepted": true,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 示例 2: 修改文章内容

**目标URL**: `jsonplaceholder.typicode.com`

**URL模式**: `posts/1`

**新响应**:
```json
{
  "userId": 1,
  "id": 1,
  "title": "Intercepted Post Title",
  "body": "This post content has been modified by HTTPS Interceptor. The original content has been replaced with this custom message.",
  "modified": true,
  "originalUrl": "https://jsonplaceholder.typicode.com/posts/1"
}
```

### 示例 3: 添加额外字段到现有响应

如果原始响应是：
```json
{
  "id": 1,
  "name": "John Doe"
}
```

配置新响应为：
```json
{
  "premium": true,
  "lastLogin": "2024-01-01T12:00:00Z",
  "features": ["advanced", "pro"]
}
```

最终用户收到的响应将是：
```json
{
  "id": 1,
  "name": "John Doe",
  "premium": true,
  "lastLogin": "2024-01-01T12:00:00Z",
  "features": ["advanced", "pro"]
}
```

## 测试功能

### 使用预设测试配置

1. 进入"拦截配置"界面
2. 点击"应用测试配置"按钮
3. 确认应用预设的测试URL和响应规则

预设的测试URL包括：
- `jsonplaceholder.typicode.com/posts/1`
- `httpbin.org/json`
- `api.github.com/users/octocat`
- `reqres.in/api/users/1`

### 验证拦截效果

1. 启动拦截服务
2. 使用浏览器或其他应用访问配置的URL
3. 查看 Logcat 输出（标签：`CustomHttpsInjector`、`LoggingInterceptor`）
4. 验证响应内容是否已被修改

## 调试技巧

### 查看日志

使用 ADB 查看详细日志：

```bash
# 查看拦截器日志
adb logcat -s CustomHttpsInjector LoggingInterceptor

# 查看所有相关日志
adb logcat | grep -E "(CustomHttpsInjector|LoggingInterceptor|NetBare)"
```

### 常见问题

1. **证书未安装**: 确保在系统设置中正确安装了自签证书
2. **VPN权限被拒绝**: 重新启动应用并授予VPN权限
3. **拦截不生效**: 检查URL配置是否正确，确保包含目标域名
4. **应用崩溃**: 检查响应内容格式是否正确（特别是JSON格式）

### 性能优化

- 避免配置过多的拦截规则
- 使用精确的URL模式匹配
- 定期清理不需要的配置

## 安全注意事项

⚠️ **重要提醒**:

1. **仅用于开发和测试**: 不要在生产环境中使用
2. **保护隐私**: 不要拦截包含敏感信息的请求
3. **遵守法律**: 确保使用符合当地法律法规
4. **网络安全**: 拦截功能可能被恶意利用，请谨慎使用

## 高级用法

### 自定义拦截器

您可以扩展 `CustomHttpsInjector` 类来实现更复杂的拦截逻辑：

```kotlin
class AdvancedHttpsInjector : SimpleHttpInjector() {
    override fun sniffResponse(response: HttpResponse): Boolean {
        // 自定义拦截条件
        return response.url().contains("your-custom-pattern")
    }
    
    override fun onResponseInject(response: HttpResponse, body: HttpBody, callback: InjectorCallback) {
        // 自定义响应修改逻辑
    }
}
```

### 动态配置

可以通过网络或文件动态加载拦截配置：

```kotlin
// 从远程服务器加载配置
fun loadRemoteConfig(url: String) {
    // 实现远程配置加载逻辑
}

// 从本地文件加载配置
fun loadLocalConfig(filePath: String) {
    // 实现本地配置文件解析
}
```

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看项目的 GitHub Issues
2. 提交新的 Issue 描述问题
3. 贡献代码改进项目

---

**免责声明**: 本工具仅供学习和研究使用，使用者需自行承担使用风险和法律责任。
