package com.example.httpsinterceptor

import android.util.Log
import com.github.megatronking.netbare.NetBareUtils
import com.github.megatronking.netbare.http.HttpBody
import com.github.megatronking.netbare.http.HttpResponse
import com.github.megatronking.netbare.http.HttpResponseHeaderPart
import com.github.megatronking.netbare.injector.InjectorCallback
import com.github.megatronking.netbare.injector.SimpleHttpInjector
import com.github.megatronking.netbare.io.HttpBodyInputStream
import com.github.megatronking.netbare.stream.ByteStream
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import java.io.ByteArrayOutputStream
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

/**
 * 自定义HTTPS响应注入器
 * 用于检测特定的HTTPS请求并修改其返回值
 */
class CustomHttpsInjector : SimpleHttpInjector() {

    companion object {
        const val TAG = "CustomHttpsInjector"
        
        // 可配置的目标URL列表
        private val TARGET_URLS = mutableSetOf<String>()
        
        // 可配置的响应修改规则
        private val RESPONSE_MODIFICATIONS = mutableMapOf<String, String>()
        
        fun addTargetUrl(url: String) {
            TARGET_URLS.add(url)
        }
        
        fun removeTargetUrl(url: String) {
            TARGET_URLS.remove(url)
        }
        
        fun addResponseModification(urlPattern: String, newResponse: String) {
            RESPONSE_MODIFICATIONS[urlPattern] = newResponse
        }
        
        fun clearTargetUrls() {
            TARGET_URLS.clear()
        }
        
        fun clearResponseModifications() {
            RESPONSE_MODIFICATIONS.clear()
        }
    }

    private var mHoldResponseHeader: HttpResponseHeaderPart? = null
    private var mTargetUrl: String? = null

    override fun sniffResponse(response: HttpResponse): Boolean {
        val url = response.url()
        
        // 检查是否匹配目标URL
        val shouldInject = TARGET_URLS.any { targetUrl ->
            url.contains(targetUrl, ignoreCase = true)
        }
        
        if (shouldInject) {
            mTargetUrl = url
            Log.i(TAG, "检测到目标HTTPS请求: $url")
            Log.i(TAG, "开始注入响应...")
        }
        
        return shouldInject
    }

    override fun onResponseInject(header: HttpResponseHeaderPart, callback: InjectorCallback) {
        // 先保存响应头，稍后可能需要修改Content-Length
        mHoldResponseHeader = header
        Log.i(TAG, "保存响应头，等待响应体...")
    }

    override fun onResponseInject(response: HttpResponse, body: HttpBody, callback: InjectorCallback) {
        var inputStream: HttpBodyInputStream? = null
        var reader: InputStreamReader? = null
        
        try {
            inputStream = HttpBodyInputStream(response, body)
            reader = InputStreamReader(inputStream, StandardCharsets.UTF_8)
            
            // 读取原始响应内容
            val originalContent = reader.readText()
            Log.i(TAG, "原始响应内容: $originalContent")
            
            // 根据URL匹配修改规则
            val modifiedContent = modifyResponse(mTargetUrl ?: "", originalContent)
            
            if (modifiedContent != originalContent) {
                Log.i(TAG, "响应内容已修改")
                Log.i(TAG, "修改后内容: $modifiedContent")
                
                // 将修改后的内容转换为字节数组
                val modifiedBytes = modifiedContent.toByteArray(StandardCharsets.UTF_8)
                
                // 更新响应头的Content-Length
                val modifiedHeader = mHoldResponseHeader!!
                    .newBuilder()
                    .replaceHeader("Content-Length", modifiedBytes.size.toString())
                    .build()
                
                // 先发送修改后的响应头
                callback.onFinished(modifiedHeader)
                
                // 再发送修改后的响应体
                callback.onFinished(ByteStream(modifiedBytes))
                
                Log.i(TAG, "HTTPS响应注入完成!")
            } else {
                // 如果没有修改，直接发送原始响应头
                callback.onFinished(mHoldResponseHeader!!)
                // 发送原始响应体
                callback.onFinished(body)
                Log.i(TAG, "响应内容未修改，直接转发")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "注入响应时发生错误", e)
            // 发生错误时，发送原始响应
            callback.onFinished(mHoldResponseHeader!!)
            callback.onFinished(body)
        } finally {
            NetBareUtils.closeQuietly(inputStream)
            NetBareUtils.closeQuietly(reader)
            mHoldResponseHeader = null
            mTargetUrl = null
        }
    }

    /**
     * 根据URL和配置的规则修改响应内容
     */
    private fun modifyResponse(url: String, originalContent: String): String {
        // 查找匹配的修改规则
        for ((urlPattern, newResponse) in RESPONSE_MODIFICATIONS) {
            if (url.contains(urlPattern, ignoreCase = true)) {
                return when {
                    // 如果是JSON响应，尝试合并
                    isJsonContent(originalContent) && isJsonContent(newResponse) -> {
                        mergeJsonResponse(originalContent, newResponse)
                    }
                    // 否则直接替换
                    else -> newResponse
                }
            }
        }
        
        // 如果没有找到匹配的规则，返回原始内容
        return originalContent
    }

    /**
     * 检查内容是否为JSON格式
     */
    private fun isJsonContent(content: String): Boolean {
        return try {
            JsonParser.parseString(content)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 合并JSON响应（将新的字段添加到原始JSON中）
     */
    private fun mergeJsonResponse(originalJson: String, newJson: String): String {
        return try {
            val originalObject = JsonParser.parseString(originalJson).asJsonObject
            val newObject = JsonParser.parseString(newJson).asJsonObject
            
            // 将新对象的所有字段添加到原始对象中
            for ((key, value) in newObject.entrySet()) {
                originalObject.add(key, value)
            }
            
            originalObject.toString()
        } catch (e: Exception) {
            Log.e(TAG, "合并JSON时发生错误", e)
            newJson // 如果合并失败，返回新的JSON
        }
    }
}
