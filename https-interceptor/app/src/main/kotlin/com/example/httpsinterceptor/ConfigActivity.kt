package com.example.httpsinterceptor

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class ConfigActivity : AppCompatActivity() {

    private lateinit var mUrlListView: RecyclerView
    private lateinit var mAddUrlButton: Button
    private lateinit var mAddRuleButton: Button
    private lateinit var mClearAllButton: Button
    private lateinit var mTestConfigButton: Button
    
    private lateinit var mUrlAdapter: UrlListAdapter
    private lateinit var mRuleAdapter: RuleListAdapter
    
    private val mTargetUrls = mutableListOf<String>()
    private val mResponseRules = mutableListOf<ResponseRule>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_config)
        
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "拦截配置"
        
        initViews()
        setupRecyclerViews()
        setupClickListeners()
        loadExistingConfig()
    }

    private fun initViews() {
        mUrlListView = findViewById(R.id.rv_urls)
        mAddUrlButton = findViewById(R.id.btn_add_url)
        mAddRuleButton = findViewById(R.id.btn_add_rule)
        mClearAllButton = findViewById(R.id.btn_clear_all)
        mTestConfigButton = findViewById(R.id.btn_test_config)
    }

    private fun setupRecyclerViews() {
        // URL列表
        mUrlAdapter = UrlListAdapter(mTargetUrls) { position ->
            removeUrl(position)
        }
        mUrlListView.layoutManager = LinearLayoutManager(this)
        mUrlListView.adapter = mUrlAdapter
        
        // 响应规则列表
        val ruleListView = findViewById<RecyclerView>(R.id.rv_rules)
        mRuleAdapter = RuleListAdapter(mResponseRules) { position ->
            removeRule(position)
        }
        ruleListView.layoutManager = LinearLayoutManager(this)
        ruleListView.adapter = mRuleAdapter
    }

    private fun setupClickListeners() {
        mAddUrlButton.setOnClickListener {
            showAddUrlDialog()
        }
        
        mAddRuleButton.setOnClickListener {
            showAddRuleDialog()
        }
        
        mClearAllButton.setOnClickListener {
            showClearAllDialog()
        }

        mTestConfigButton.setOnClickListener {
            applyTestConfiguration()
        }
    }

    private fun showAddUrlDialog() {
        val editText = EditText(this)
        editText.hint = "输入要拦截的URL (例如: api.example.com)"
        
        AlertDialog.Builder(this)
            .setTitle("添加目标URL")
            .setView(editText)
            .setPositiveButton("添加") { _, _ ->
                val url = editText.text.toString().trim()
                if (url.isNotEmpty()) {
                    addUrl(url)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showAddRuleDialog() {
        val view = layoutInflater.inflate(R.layout.dialog_add_rule, null)
        val urlPatternEdit = view.findViewById<EditText>(R.id.et_url_pattern)
        val responseEdit = view.findViewById<EditText>(R.id.et_response)
        
        AlertDialog.Builder(this)
            .setTitle("添加响应修改规则")
            .setView(view)
            .setPositiveButton("添加") { _, _ ->
                val urlPattern = urlPatternEdit.text.toString().trim()
                val response = responseEdit.text.toString().trim()
                if (urlPattern.isNotEmpty() && response.isNotEmpty()) {
                    addRule(urlPattern, response)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showClearAllDialog() {
        AlertDialog.Builder(this)
            .setTitle("清空所有配置")
            .setMessage("确定要清空所有URL和响应规则吗？")
            .setPositiveButton("确定") { _, _ ->
                clearAll()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun addUrl(url: String) {
        if (!mTargetUrls.contains(url)) {
            mTargetUrls.add(url)
            mUrlAdapter.notifyItemInserted(mTargetUrls.size - 1)
            CustomHttpsInjector.addTargetUrl(url)
            Toast.makeText(this, "已添加URL: $url", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "URL已存在", Toast.LENGTH_SHORT).show()
        }
    }

    private fun removeUrl(position: Int) {
        if (position < mTargetUrls.size) {
            val url = mTargetUrls.removeAt(position)
            mUrlAdapter.notifyItemRemoved(position)
            CustomHttpsInjector.removeTargetUrl(url)
            Toast.makeText(this, "已删除URL: $url", Toast.LENGTH_SHORT).show()
        }
    }

    private fun addRule(urlPattern: String, response: String) {
        val rule = ResponseRule(urlPattern, response)
        mResponseRules.add(rule)
        mRuleAdapter.notifyItemInserted(mResponseRules.size - 1)
        CustomHttpsInjector.addResponseModification(urlPattern, response)
        Toast.makeText(this, "已添加响应规则", Toast.LENGTH_SHORT).show()
    }

    private fun removeRule(position: Int) {
        if (position < mResponseRules.size) {
            val rule = mResponseRules.removeAt(position)
            mRuleAdapter.notifyItemRemoved(position)
            // 注意：这里简化处理，实际应该重新设置所有规则
            Toast.makeText(this, "已删除响应规则", Toast.LENGTH_SHORT).show()
        }
    }

    private fun clearAll() {
        mTargetUrls.clear()
        mResponseRules.clear()
        mUrlAdapter.notifyDataSetChanged()
        mRuleAdapter.notifyDataSetChanged()
        
        CustomHttpsInjector.clearTargetUrls()
        CustomHttpsInjector.clearResponseModifications()
        
        Toast.makeText(this, "已清空所有配置", Toast.LENGTH_SHORT).show()
    }

    private fun loadExistingConfig() {
        // 这里可以从SharedPreferences或数据库加载已保存的配置
        // 为了演示，添加一些示例配置
        addUrl("jsonplaceholder.typicode.com")
        addRule("posts/1", """{"id":1,"title":"Modified Title","body":"This is a modified response","userId":1}""")
    }

    private fun applyTestConfiguration() {
        AlertDialog.Builder(this)
            .setTitle("应用测试配置")
            .setMessage("这将清空当前配置并应用预设的测试配置，包括多个测试URL和响应规则。确定继续吗？")
            .setPositiveButton("确定") { _, _ ->
                // 清空当前配置
                clearAll()

                // 应用测试配置
                TestUtils.applyTestConfiguration()

                // 更新UI显示
                TestUtils.getTestUrls().forEach { url ->
                    val domain = extractDomain(url)
                    if (!mTargetUrls.contains(domain)) {
                        mTargetUrls.add(domain)
                    }
                }

                TestUtils.getTestRules().forEach { (pattern, response) ->
                    mResponseRules.add(ResponseRule(pattern, response))
                }

                mUrlAdapter.notifyDataSetChanged()
                mRuleAdapter.notifyDataSetChanged()

                Toast.makeText(this, "测试配置已应用", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun extractDomain(url: String): String {
        return try {
            val urlObj = java.net.URL(url)
            urlObj.host
        } catch (e: Exception) {
            url
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    data class ResponseRule(
        val urlPattern: String,
        val response: String
    )
}
