package com.example.httpsinterceptor

import android.util.Log
import com.github.megatronking.netbare.http.HttpIndexedInterceptor
import com.github.megatronking.netbare.http.HttpInterceptorFactory
import com.github.megatronking.netbare.http.HttpRequestChain
import com.github.megatronking.netbare.http.HttpResponseChain
import java.nio.ByteBuffer

/**
 * 日志拦截器 - 用于记录所有HTTP请求和响应
 * 方便调试和监控拦截效果
 */
class LoggingInterceptor : HttpIndexedInterceptor() {

    companion object {
        const val TAG = "LoggingInterceptor"
        
        fun createFactory(): HttpInterceptorFactory {
            return HttpInterceptorFactory { LoggingInterceptor() }
        }
    }

    override fun intercept(chain: HttpRequestChain, buffer: ByteBuffer, index: Int) {
        if (index == 0) {
            // 只在收到第一个数据包时记录请求信息
            val request = chain.request()
            Log.i(TAG, "=== HTTP Request ===")
            Log.i(TAG, "URL: ${request.url()}")
            Log.i(TAG, "Method: ${request.method()}")
            Log.i(TAG, "Headers: ${request.requestHeaders()}")
            Log.i(TAG, "UID: ${request.uid()}")
        }
        
        // 继续处理请求
        chain.process(buffer)
    }

    override fun intercept(chain: HttpResponseChain, buffer: ByteBuffer, index: Int) {
        if (index == 0) {
            // 只在收到第一个数据包时记录响应信息
            val response = chain.response()
            Log.i(TAG, "=== HTTP Response ===")
            Log.i(TAG, "URL: ${response.url()}")
            Log.i(TAG, "Status: ${response.code()} ${response.message()}")
            Log.i(TAG, "Headers: ${response.responseHeaders()}")
            Log.i(TAG, "Content-Type: ${response.contentType()}")
            Log.i(TAG, "Content-Length: ${response.contentLength()}")
        }
        
        // 继续处理响应
        chain.process(buffer)
    }
}
