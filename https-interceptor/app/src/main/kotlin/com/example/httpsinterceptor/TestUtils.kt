package com.example.httpsinterceptor

import android.content.Context
import android.widget.Toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

/**
 * 测试工具类 - 用于测试HTTPS拦截功能
 */
object TestUtils {

    /**
     * 测试HTTP请求，用于验证拦截功能
     */
    fun testHttpRequest(context: Context, url: String) {
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val connection = URL(url).openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                val responseCode = connection.responseCode
                val response = if (responseCode == 200) {
                    BufferedReader(InputStreamReader(connection.inputStream)).use { reader ->
                        reader.readText()
                    }
                } else {
                    "HTTP Error: $responseCode"
                }
                
                withContext(Dispatchers.Main) {
                    showTestResult(context, url, responseCode, response)
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(context, "请求失败: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun showTestResult(context: Context, url: String, code: Int, response: String) {
        val shortResponse = if (response.length > 200) {
            response.take(200) + "..."
        } else {
            response
        }
        
        val message = "测试结果:\nURL: $url\n状态码: $code\n响应: $shortResponse"
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    /**
     * 获取预设的测试URL列表
     */
    fun getTestUrls(): List<String> {
        return listOf(
            "https://jsonplaceholder.typicode.com/posts/1",
            "https://httpbin.org/json",
            "https://api.github.com/users/octocat",
            "https://reqres.in/api/users/1"
        )
    }

    /**
     * 获取预设的测试响应规则
     */
    fun getTestRules(): List<Pair<String, String>> {
        return listOf(
            "posts/1" to """{"id":1,"title":"Intercepted Title","body":"This response was modified by HTTPS Interceptor","userId":1,"intercepted":true}""",
            "json" to """{"modified":true,"message":"Response intercepted successfully","timestamp":"${System.currentTimeMillis()}"}""",
            "users/octocat" to """{"login":"intercepted_user","id":1,"name":"Intercepted User","bio":"This user data was modified"}""",
            "users/1" to """{"id":1,"email":"<EMAIL>","first_name":"Modified","last_name":"User","intercepted":true}"""
        )
    }

    /**
     * 应用测试配置
     */
    fun applyTestConfiguration() {
        // 清空现有配置
        CustomHttpsInjector.clearTargetUrls()
        CustomHttpsInjector.clearResponseModifications()
        
        // 添加测试URL
        getTestUrls().forEach { url ->
            val domain = extractDomain(url)
            CustomHttpsInjector.addTargetUrl(domain)
        }
        
        // 添加测试规则
        getTestRules().forEach { (pattern, response) ->
            CustomHttpsInjector.addResponseModification(pattern, response)
        }
    }

    private fun extractDomain(url: String): String {
        return try {
            val urlObj = URL(url)
            urlObj.host
        } catch (e: Exception) {
            url
        }
    }
}
