package com.example.httpsinterceptor

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.github.megatronking.netbare.NetBare
import com.github.megatronking.netbare.NetBareConfig
import com.github.megatronking.netbare.NetBareListener
import com.github.megatronking.netbare.http.HttpInjectInterceptor
import com.github.megatronking.netbare.http.HttpInterceptorFactory
import com.github.megatronking.netbare.ssl.JKS
import java.io.IOException

class MainActivity : AppCompatActivity(), NetBareListener {

    companion object {
        private const val REQUEST_CODE_PREPARE = 1001
    }

    private lateinit var mNetBare: NetBare
    private lateinit var mActionButton: Button
    private lateinit var mStatusText: TextView
    private lateinit var mConfigButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        initNetBare()
        setupClickListeners()
    }

    private fun initViews() {
        mActionButton = findViewById(R.id.btn_action)
        mStatusText = findViewById(R.id.tv_status)
        mConfigButton = findViewById(R.id.btn_config)
        
        updateUI()
    }

    private fun initNetBare() {
        mNetBare = NetBare.get()
        mNetBare.registerNetBareListener(this)
    }

    private fun setupClickListeners() {
        mActionButton.setOnClickListener {
            if (mNetBare.isActive) {
                stopInterceptor()
            } else {
                startInterceptor()
            }
        }

        mConfigButton.setOnClickListener {
            startActivity(Intent(this, ConfigActivity::class.java))
        }
    }

    private fun startInterceptor() {
        // 检查并安装自签证书
        if (!JKS.isInstalled(this, App.JKS_ALIAS)) {
            try {
                JKS.install(this, App.JKS_ALIAS, App.JKS_ALIAS)
                Toast.makeText(this, "请在设置中安装证书后重试", Toast.LENGTH_LONG).show()
                return
            } catch (e: IOException) {
                Toast.makeText(this, "证书安装失败: ${e.message}", Toast.LENGTH_LONG).show()
                return
            }
        }

        // 准备VPN权限
        val intent = mNetBare.prepare()
        if (intent != null) {
            startActivityForResult(intent, REQUEST_CODE_PREPARE)
            return
        }

        // 启动NetBare服务
        startNetBareService()
    }

    private fun stopInterceptor() {
        mNetBare.stop()
    }

    private fun startNetBareService() {
        try {
            val config = NetBareConfig.defaultHttpConfig(
                App.getInstance().getJKS(),
                createInterceptorFactories()
            )
            mNetBare.start(config)
        } catch (e: Exception) {
            Toast.makeText(this, "启动服务失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun createInterceptorFactories(): List<HttpInterceptorFactory> {
        // 创建日志拦截器（用于调试）
        val loggingInterceptor = LoggingInterceptor.createFactory()

        // 创建自定义HTTPS注入器
        val httpsInjector = HttpInjectInterceptor.createFactory(CustomHttpsInjector())

        // 注意：拦截器的顺序很重要，日志拦截器应该在注入器之前
        return listOf(loggingInterceptor, httpsInjector)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == REQUEST_CODE_PREPARE) {
            startNetBareService()
        } else if (requestCode == REQUEST_CODE_PREPARE) {
            Toast.makeText(this, "需要VPN权限才能使用拦截功能", Toast.LENGTH_LONG).show()
        }
    }

    override fun onServiceStarted() {
        runOnUiThread {
            updateUI()
            Toast.makeText(this, "HTTPS拦截服务已启动", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onServiceStopped() {
        runOnUiThread {
            updateUI()
            Toast.makeText(this, "HTTPS拦截服务已停止", Toast.LENGTH_SHORT).show()
        }
    }

    private fun updateUI() {
        if (mNetBare.isActive) {
            mActionButton.text = "停止拦截"
            mStatusText.text = "状态: 正在拦截HTTPS请求"
            mStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
        } else {
            mActionButton.text = "开始拦截"
            mStatusText.text = "状态: 未启动"
            mStatusText.setTextColor(getColor(android.R.color.holo_red_dark))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mNetBare.unregisterNetBareListener(this)
        mNetBare.stop()
    }
}
