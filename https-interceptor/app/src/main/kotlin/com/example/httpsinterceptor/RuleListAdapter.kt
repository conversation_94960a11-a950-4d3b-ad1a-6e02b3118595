package com.example.httpsinterceptor

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class RuleListAdapter(
    private val rules: MutableList<ConfigActivity.ResponseRule>,
    private val onDeleteClick: (Int) -> Unit
) : RecyclerView.Adapter<RuleListAdapter.ViewHolder>() {

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val urlPatternText: TextView = view.findViewById(R.id.tv_url_pattern)
        val responseText: TextView = view.findViewById(R.id.tv_response)
        val deleteButton: ImageButton = view.findViewById(R.id.btn_delete)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_rule, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val rule = rules[position]
        holder.urlPatternText.text = "URL模式: ${rule.urlPattern}"
        holder.responseText.text = "响应: ${rule.response.take(50)}${if (rule.response.length > 50) "..." else ""}"
        holder.deleteButton.setOnClickListener {
            onDeleteClick(position)
        }
    }

    override fun getItemCount(): Int = rules.size
}
