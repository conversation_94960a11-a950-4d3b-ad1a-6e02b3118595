package com.example.httpsinterceptor

import android.app.Application
import com.github.megatronking.netbare.NetBare
import com.github.megatronking.netbare.ssl.JKS
import me.weishu.reflection.Reflection

class App : Application() {

    companion object {
        const val JKS_ALIAS = "HttpsInterceptor"
        
        private lateinit var sInstance: App
        
        fun getInstance(): App {
            return sInstance
        }
    }

    private lateinit var mJKS: JKS

    override fun onCreate() {
        super.onCreate()
        sInstance = this
        
        // 解决 Android P+ 反射限制
        Reflection.unseal(this)
        
        // 创建自签证书
        mJKS = JKS(
            this, 
            JKS_ALIAS, 
            JKS_ALIAS.toCharArray(), 
            JKS_ALIAS,
            JKS_ALIAS,
            JKS_ALIAS, 
            JKS_ALIAS, 
            JKS_ALIAS
        )

        // 初始化NetBare
        NetBare.get().attachApplication(this, BuildConfig.DEBUG)
    }

    fun getJKS(): JKS {
        return mJKS
    }
}
