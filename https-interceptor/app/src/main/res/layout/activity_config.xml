<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 目标URL配置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="目标URL配置"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="添加要拦截的URL模式（支持部分匹配）"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_add_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="+ 添加目标URL"
            android:layout_marginBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_urls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:nestedScrollingEnabled="false" />

        <!-- 响应修改规则 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="响应修改规则"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="配置URL模式对应的新响应内容"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_add_rule"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="+ 添加响应规则"
            android:layout_marginBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_rules"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:nestedScrollingEnabled="false" />

        <!-- 操作按钮 -->
        <Button
            android:id="@+id/btn_test_config"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="应用测试配置"
            android:layout_marginBottom="8dp"
            android:backgroundTint="@android:color/holo_blue_light"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btn_clear_all"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="清空所有配置"
            android:backgroundTint="@android:color/holo_red_light"
            android:textColor="@android:color/white" />

    </LinearLayout>

</ScrollView>
