<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="HTTPS 请求拦截器"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="状态: 未启动"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="12dp"
        android:background="@drawable/status_background"
        android:textColor="@android:color/holo_red_dark" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="功能说明："
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="• 拦截指定的HTTPS请求\n• 修改响应内容\n• 支持JSON响应合并\n• 实时生效，无需重启应用"
        android:textSize="14sp"
        android:layout_marginBottom="32dp"
        android:lineSpacingExtra="4dp" />

    <Button
        android:id="@+id/btn_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开始拦截"
        android:textSize="18sp"
        android:layout_marginBottom="16dp"
        android:padding="16dp" />

    <Button
        android:id="@+id/btn_config"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="拦截配置"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="12dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="注意事项："
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="1. 首次使用需要安装自签证书\n2. 需要授予VPN权限\n3. 仅用于学习和调试目的"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:lineSpacingExtra="2dp" />

</LinearLayout>
