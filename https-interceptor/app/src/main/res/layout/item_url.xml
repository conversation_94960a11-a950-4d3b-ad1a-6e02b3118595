<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="@drawable/item_background"
    android:layout_marginBottom="8dp">

    <TextView
        android:id="@+id/tv_url"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="example.com/api"
        android:textSize="16sp"
        android:layout_gravity="center_vertical" />

    <ImageButton
        android:id="@+id/btn_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@android:drawable/ic_menu_delete"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="删除" />

</LinearLayout>
