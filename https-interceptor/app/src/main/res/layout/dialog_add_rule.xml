<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="URL模式"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_url_pattern"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="例如: api/posts/1"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="新的响应内容"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_response"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:hint="例如: {&quot;id&quot;:1,&quot;title&quot;:&quot;Modified&quot;}"
        android:gravity="top"
        android:inputType="textMultiLine"
        android:scrollbars="vertical" />

</LinearLayout>
