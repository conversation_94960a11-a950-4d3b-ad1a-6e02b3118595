<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="@drawable/item_background"
    android:layout_marginBottom="8dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_url_pattern"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="URL模式: api/posts"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_response"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="响应: {\"modified\": true}"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:maxLines="2"
            android:ellipsize="end" />

    </LinearLayout>

    <ImageButton
        android:id="@+id/btn_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@android:drawable/ic_menu_delete"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="删除"
        android:layout_gravity="center_vertical" />

</LinearLayout>
