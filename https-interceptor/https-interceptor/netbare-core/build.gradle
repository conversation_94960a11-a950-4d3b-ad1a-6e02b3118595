apply plugin: 'com.android.library'

android {
    namespace 'com.github.netbare.core'  // 添加这一行
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 21
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'com.android.support:appcompat-v7:28.0.0'

    implementation 'org.bouncycastle:bcpkix-jdk15on:1.56'
    implementation 'org.bouncycastle:bcprov-jdk15on:1.56'
    implementation 'com.google.guava:guava:19.0'
}
