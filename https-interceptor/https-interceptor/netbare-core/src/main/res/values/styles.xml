<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="NetBare" parent="android:Theme.Translucent.NoTitleBar.Fullscreen">
        <item name="android:windowAnimationStyle">@style/NetBare_NoAnimation</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="NetBare_NoAnimation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>
</resources>