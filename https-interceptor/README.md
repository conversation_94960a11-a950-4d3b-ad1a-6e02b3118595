# HTTPS Interceptor

基于 NetBare 框架的 HTTPS 请求拦截和响应修改工具。

## 功能特性

- 🔍 **智能拦截**: 支持配置多个目标URL模式进行精准拦截
- 🔧 **响应修改**: 支持修改HTTPS响应内容，包括JSON、HTML等格式
- 🔄 **JSON合并**: 智能合并JSON响应，保留原有字段并添加新字段
- ⚡ **实时生效**: 配置修改后立即生效，无需重启应用
- 🛡️ **安全可控**: 基于VPN技术，仅拦截指定的请求

## 使用方法

### 1. 环境准备

确保您的开发环境中包含 NetBare 库：

```bash
# 将 NetBare 项目复制到当前目录
cp -r /path/to/NetBare-Android-master/netbare-core ./
cp -r /path/to/NetBare-Android-master/netbare-injector ./
```

### 2. 安装和配置

1. 打开应用，首次使用会提示安装自签证书
2. 在系统设置中安装证书（设置 > 安全 > 加密与凭据 > 从存储设备安装）
3. 授予应用VPN权限

### 3. 配置拦截规则

1. 点击"拦截配置"按钮
2. 添加目标URL（支持部分匹配，如：`api.example.com`）
3. 添加响应修改规则：
   - URL模式：要匹配的URL部分
   - 响应内容：新的响应数据

### 4. 开始拦截

1. 返回主界面
2. 点击"开始拦截"按钮
3. 应用会在通知栏显示拦截状态

## 配置示例

### 拦截JSON API响应

**目标URL**: `jsonplaceholder.typicode.com`

**URL模式**: `posts/1`

**新响应**:
```json
{
  "id": 1,
  "title": "Modified Title",
  "body": "This response has been modified by HTTPS Interceptor",
  "userId": 1,
  "modified": true,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 拦截图片请求

**目标URL**: `example.com/images`

**URL模式**: `logo.png`

**新响应**: 可以是新的图片数据或重定向响应

## 技术原理

1. **VPN隧道**: 使用Android VPN API创建虚拟网络接口
2. **流量转发**: 将目标应用的网络流量转发到本地代理服务器
3. **SSL解密**: 使用自签证书对HTTPS流量进行解密
4. **内容修改**: 在代理层面修改HTTP响应内容
5. **重新加密**: 将修改后的内容重新加密发送给客户端

## 注意事项

⚠️ **重要提醒**:
- 本工具仅用于学习和调试目的
- 请勿用于非法用途或恶意攻击
- 使用前请确保遵守相关法律法规
- 建议在测试环境中使用

## 依赖库

- [NetBare](https://github.com/MegatronKing/NetBare): 网络包拦截和注入框架
- Gson: JSON解析库
- AndroidX: Android支持库

## 许可证

本项目基于 Apache 2.0 许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
