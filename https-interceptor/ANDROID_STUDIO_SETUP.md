# Android Studio 项目设置指南

## 问题说明

您在 Android Studio 中看不到 `https-interceptor` 项目是因为：

1. 项目缺少完整的 Gradle Wrapper 文件
2. NetBare 依赖库还没有复制到项目中
3. 项目需要作为独立项目打开，而不是作为子模块

## 解决方案

### 方法 1: 手动设置（推荐）

#### 步骤 1: 复制必要文件

在命令行中执行以下操作：

```bash
# 进入 NetBare 项目根目录
cd H:\NetBare-Android-master\NetBare-Android-master

# 复制 NetBare 库到 https-interceptor 项目
xcopy netbare-core https-interceptor\netbare-core /E /I /Y
xcopy netbare-injector https-interceptor\netbare-injector /E /I /Y

# 复制 Gradle Wrapper
copy gradle\wrapper\gradle-wrapper.jar https-interceptor\gradle\wrapper\gradle-wrapper.jar
```

#### 步骤 2: 在 Android Studio 中打开项目

1. 打开 Android Studio
2. 选择 "Open an existing Android Studio project"
3. 导航到 `H:\NetBare-Android-master\NetBare-Android-master\https-interceptor`
4. 选择 `https-interceptor` 文件夹
5. 点击 "OK"

#### 步骤 3: 等待 Gradle 同步

- Android Studio 会自动开始 Gradle 同步
- 等待同步完成（可能需要几分钟）
- 如果出现错误，点击 "Try Again"

### 方法 2: 使用设置脚本

#### Windows:
```cmd
cd https-interceptor
setup.bat
```

#### Linux/macOS:
```bash
cd https-interceptor
chmod +x setup.sh
./setup.sh
```

### 方法 3: 创建新的独立项目（备选方案）

如果上述方法不工作，您可以：

1. 在 Android Studio 中创建新的空项目
2. 将 `https-interceptor/app/src` 目录的内容复制到新项目
3. 复制 `build.gradle` 文件的依赖配置
4. 手动添加 NetBare 库

## 验证项目设置

### 检查项目结构

确保您的项目结构如下：

```
https-interceptor/
├── app/
│   ├── src/main/
│   │   ├── kotlin/com/example/httpsinterceptor/
│   │   ├── res/
│   │   └── AndroidManifest.xml
│   └── build.gradle
├── netbare-core/
│   ├── src/
│   └── build.gradle
├── netbare-injector/
│   ├── src/
│   └── build.gradle
├── gradle/wrapper/
│   ├── gradle-wrapper.jar
│   └── gradle-wrapper.properties
├── build.gradle
├── settings.gradle
├── gradlew
└── gradlew.bat
```

### 检查 Gradle 同步

在 Android Studio 中：

1. 查看底部状态栏是否显示 "Gradle sync in progress"
2. 等待同步完成
3. 检查 "Build" 窗口是否有错误信息

### 检查模块配置

在 Android Studio 中：

1. 打开 "Project" 视图
2. 确保看到以下模块：
   - `app`
   - `netbare-core`
   - `netbare-injector`

## 常见问题解决

### Q1: "Project not found" 错误

**解决方案**:
- 确保选择的是 `https-interceptor` 文件夹，不是其父目录
- 检查文件夹中是否包含 `build.gradle` 和 `settings.gradle` 文件

### Q2: Gradle 同步失败

**解决方案**:
1. 点击 "File" → "Invalidate Caches and Restart"
2. 重新打开项目
3. 检查网络连接
4. 更新 Android Studio 到最新版本

### Q3: NetBare 库找不到

**解决方案**:
1. 确保 `netbare-core` 和 `netbare-injector` 目录存在
2. 检查这些目录中是否包含 `build.gradle` 文件
3. 重新运行设置脚本

### Q4: 编译错误

**解决方案**:
1. 检查 Android SDK 版本是否正确
2. 更新 Gradle 版本
3. 清理项目：Build → Clean Project
4. 重新构建：Build → Rebuild Project

## 快速验证

项目设置成功后，您应该能够：

1. ✅ 在 Android Studio 中看到项目结构
2. ✅ 成功编译项目（Build → Make Project）
3. ✅ 在设备上运行应用
4. ✅ 看到应用的主界面

## 下一步

项目设置完成后：

1. 连接 Android 设备或启动模拟器
2. 点击 "Run" 按钮运行应用
3. 按照应用内的指导完成证书安装和权限授予
4. 开始配置和测试 HTTPS 拦截功能

## 需要帮助？

如果仍然遇到问题：

1. 检查 Android Studio 的 "Event Log" 窗口查看详细错误信息
2. 确保您的开发环境满足要求：
   - Android Studio 4.0+
   - JDK 8+
   - Android SDK API 21+
3. 尝试创建一个简单的新 Android 项目来验证您的开发环境是否正常工作
